<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TripwiseGO Feature Requests</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .transition-all {
            transition: all 0.3s ease;
        }

        .post-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .vote-button {
            transition: color 0.2s ease, transform 0.2s ease;
        }

        .vote-button:hover {
            transform: scale(1.1);
        }

        .vote-button.voted {
            font-weight: bold;
        }

        .vote-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>

<body class="bg-white text-gray-800 transition-all min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <header class="mb-8">
            <div class="flex justify-between items-start">
                <div class="flex flex-col items-start">
                    <img src="logo_blue.png" alt="TripwiseGO Logo" class="h-12 w-auto mb-2" onerror="this.style.display='none'; document.getElementById('fallback-text').style.display='block';">
                    <h1 id="fallback-text" class="text-3xl font-bold text-indigo-600 dark:text-indigo-400 mb-2" style="display: none;">TripwiseGO</h1>
                    <p class="text-gray-600 dark:text-gray-400">Feature Requests</p>
                </div>
                <div id="connection-status" class="text-sm px-3 py-1 rounded-full">
                    <!-- Status will be updated by JavaScript -->
                </div>
            </div>
        </header>

        <!-- Add Feature Button and Sort Controls -->
        <div class="flex justify-between items-center mb-8">
            <button id="refresh-btn" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-all flex items-center">
                <i class="fas fa-sync-alt mr-2"></i> Refresh
            </button>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                <select id="sort-select" class="px-3 py-1 border border-gray-300 rounded-md bg-white text-sm">
                    <option value="votes">Most Voted</option>
                    <option value="newest">Newest First</option>
                </select>
            </div>
        </div>

        <!-- Feature Request Modal -->
        <div id="feature-modal"
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4 transition-all">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold">Suggest a new feature</h2>
                    <button id="close-modal-btn" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="feature-form" class="space-y-4">
                    <div>
                        <label for="title"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Title</label>
                        <input type="text" id="title" name="title" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="description"
                            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                        <textarea id="description" name="description" rows="3" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                    </div>
                    <button type="submit"
                        class="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-all flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i> Submit Feature Request
                    </button>
                </form>
            </div>
        </div>

        <!-- Feature Requests List -->
        <div id="feature-list" class="space-y-4">
            <!-- Feature cards will be dynamically inserted here -->
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="text-center py-12">
            <i class="fas fa-lightbulb text-5xl text-gray-400 mb-4"></i>
            <h3 class="text-xl font-medium text-gray-600">No feature requests yet</h3>
            <p class="text-gray-500 mt-2">Be the first to suggest an improvement for TripwiseGO!</p>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button id="open-form-btn"
        class="fixed bottom-8 right-8 w-16 h-16 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full shadow-lg transition-all flex items-center justify-center z-40">
        <i class="fas fa-plus text-2xl"></i>
    </button>

    <script>
        // Supabase Backend Configuration
        const API_BASE_URL = window.location.protocol === 'file:'
            ? 'http://localhost:3000/api'
            : '/api';

        // Feature request data - now loaded from Google Apps Script
        let features = [];
        // DOM elements
        const featureForm = document.getElementById('feature-form');
        const featureList = document.getElementById('feature-list');
        const emptyState = document.getElementById('empty-state');
        const openFormBtn = document.getElementById('open-form-btn');
        const closeModalBtn = document.getElementById('close-modal-btn');
        const featureModal = document.getElementById('feature-modal');
        const sortSelect = document.getElementById('sort-select');
        const refreshBtn = document.getElementById('refresh-btn');
        // Display features
        function displayFeatures() {
            featureList.innerHTML = '';

            if (features.length === 0) {
                emptyState.classList.remove('hidden');
                return;
            }

            emptyState.classList.add('hidden');

            // Sort features based on selected option
            const sortBy = sortSelect.value;
            let sortedFeatures = [...features];

            if (sortBy === 'votes') {
                sortedFeatures.sort((a, b) => (b.votes || 0) - (a.votes || 0));
            } else {
                sortedFeatures.sort((a, b) => new Date(b.date) - new Date(a.date));
            }

            sortedFeatures.forEach((feature, index) => {
                const featureCard = document.createElement('div');
                featureCard.className = `post-card bg-white rounded-lg shadow-md p-6 transition-all duration-300 ease-in-out`;
                featureCard.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex flex-col items-center mr-4">
                            <button class="vote-button text-gray-500 hover:text-indigo-600" data-id="${feature.id}" data-vote="up">
                                <i class="fas fa-arrow-up text-xl"></i>
                            </button>
                            <span class="my-1 font-semibold text-gray-700 dark:text-gray-300">${feature.votes || 0}</span>
                            <button class="vote-button text-gray-500 hover:text-red-600" data-id="${feature.id}" data-vote="down">
                                <i class="fas fa-arrow-down text-xl"></i>
                            </button>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold mb-1">${feature.title}</h3>
                            <p class="text-gray-600 dark:text-gray-400 mb-3">${feature.description}</p>
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-500">
                                <span>Posted ${formatDate(feature.date)}</span>
                                <span class="mx-2">•</span>
                                ${feature.creatorId === localStorage.getItem('tripwisego-user-id') ?
                        `<button class="text-indigo-600 hover:underline" data-id="${feature.id}" data-action="delete">
                                    <i class="fas fa-trash mr-1"></i> Delete
                                </button>` : ''}
                            </div>
                        </div>
                        ${sortSelect.value === 'votes' && index === 0 && (feature.votes || 0) > 0 ? '<div class="ml-4 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-semibold">Top Voted</div>' : ''}
                    </div>
                `;
                featureList.appendChild(featureCard);
            });

            // Add event listeners to vote buttons
            document.querySelectorAll('.vote-button').forEach(button => {
                button.addEventListener('click', handleVote);
            });

            // Update vote button states to show user's previous votes
            updateVoteButtonStates();

            // Add event listeners to delete buttons
            document.querySelectorAll('[data-action="delete"]').forEach(button => {
                button.addEventListener('click', handleDelete);
            });
        }
        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
            return `${Math.floor(diffInSeconds / 86400)} days ago`;
        }
        // Vote tracking to prevent duplicate votes
        let userVotes = JSON.parse(localStorage.getItem('tripwisego-user-votes') || '{}');

        // Get or create user ID for vote tracking
        function getUserId() {
            let userId = localStorage.getItem('tripwisego-user-id');
            if (!userId) {
                userId = 'user-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('tripwisego-user-id', userId);
            }
            return userId;
        }

        // Check if user has already voted on a feature
        function hasUserVoted(featureId, userId) {
            return userVotes[userId] && userVotes[userId][featureId];
        }

        // Record user vote locally
        function recordUserVote(featureId, userId, voteType) {
            if (!userVotes[userId]) {
                userVotes[userId] = {};
            }
            userVotes[userId][featureId] = {
                voteType: voteType,
                timestamp: new Date().toISOString()
            };
            localStorage.setItem('tripwisego-user-votes', JSON.stringify(userVotes));
        }

        // Get user's vote for a feature
        function getUserVote(featureId, userId) {
            return userVotes[userId] && userVotes[userId][featureId];
        }

        // Validate vote parameters
        function validateVoteParams(featureId, voteType, userId) {
            if (!featureId) {
                throw new Error('Feature ID is required for voting');
            }

            if (!voteType || (voteType !== 'up' && voteType !== 'down')) {
                throw new Error('Vote type must be "up" or "down"');
            }

            if (!userId) {
                throw new Error('User ID is required for voting');
            }

            // Check if feature exists
            const feature = features.find(f => f.id === featureId);
            if (!feature) {
                throw new Error('Feature not found');
            }

            return true;
        }

        // Get vote statistics for debugging
        function getVoteStats() {
            const userId = getUserId();
            const userVoteCount = userVotes[userId] ? Object.keys(userVotes[userId]).length : 0;
            const totalUsers = Object.keys(userVotes).length;

            console.log('Vote Statistics:');
            console.log('- Current user ID:', userId);
            console.log('- Current user votes:', userVoteCount);
            console.log('- Total users who voted:', totalUsers);
            console.log('- User votes data:', userVotes[userId] || {});

            return {
                userId,
                userVoteCount,
                totalUsers,
                userVotes: userVotes[userId] || {}
            };
        }

        // Expose vote stats function globally for debugging
        window.getVoteStats = getVoteStats;

        // ============================================================================
        // SUPABASE BACKEND INTEGRATION - COMPLETE CRUD OPERATIONS
        // ============================================================================

        // Load features from Supabase backend
        async function loadFeaturesFromSupabase() {
            try {
                console.log('Loading features from Supabase backend:', API_BASE_URL);

                const response = await fetch(`${API_BASE_URL}/features`, {
                    method: 'GET',
                    headers: {
                        'Cache-Control': 'no-cache'
                    },
                    signal: AbortSignal.timeout(15000) // 15 second timeout
                });

                if (!response.ok) {
                    if (response.status === 404) {
                        throw new Error('API endpoint not found. Please check the server configuration.');
                    } else if (response.status === 403) {
                        throw new Error('Access denied to API. Please check permissions.');
                    } else if (response.status >= 500) {
                        throw new Error('Server error. Please try again later.');
                    } else {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                }

                const result = await response.json();

                if (result.success && result.data) {
                    features = result.data;
                    localStorage.setItem('tripwisego-features-cache', JSON.stringify(features));
                    displayFeatures();
                    console.log('Features loaded from Supabase successfully');
                    return true;
                } else {
                    throw new Error(result.error || 'Invalid response from server');
                }
            } catch (error) {
                console.error('Error loading features from Supabase:', error);

                // Provide specific error messages
                let errorMessage = 'Unable to load features.';
                if (error.name === 'AbortError' || error.message.includes('timeout')) {
                    errorMessage = 'Request timeout. Server took too long to respond.';
                } else if (error.message.includes('Failed to fetch') || error.message.includes('Network error')) {
                    errorMessage = 'Network error. Please check your internet connection.';
                } else if (error.message.includes('not found')) {
                    errorMessage = 'API endpoint not found. Please check the server configuration.';
                } else if (error.message.includes('Access denied')) {
                    errorMessage = 'Access denied. Please check API permissions.';
                } else if (error.message.includes('server error')) {
                    errorMessage = 'Server error. Please try again later.';
                }

                // Load cached data as fallback
                const cachedFeatures = localStorage.getItem('tripwisego-features-cache');
                if (cachedFeatures) {
                    features = JSON.parse(cachedFeatures);
                    displayFeatures();
                    console.log('Displaying cached features (offline mode)');
                    showNotification(`${errorMessage} Using cached data.`, 'info');
                } else {
                    displayFeatures(); // Show empty state
                    showNotification(errorMessage, 'error');
                }
                return false;
            }
        }

        // Add a new feature to Supabase
        async function addFeatureToSupabase(feature) {
            try {
                console.log('Adding feature to Supabase:', feature);

                const response = await fetch(`${API_BASE_URL}/upload-feature`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(feature),
                    signal: AbortSignal.timeout(30000)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || 'Failed to add feature');
                }
            } catch (error) {
                console.error('Error adding feature to Supabase:', error);
                throw new Error(`Failed to add feature: ${error.message}`);
            }
        }

        // Update an existing feature via Supabase API
        async function updateFeatureInSupabase(featureId, updates) {
            try {
                console.log('Updating feature in Supabase:', featureId, updates);

                const response = await fetch(`${API_BASE_URL}/features/${featureId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updates),
                    signal: AbortSignal.timeout(15000)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || 'Failed to update feature');
                }
            } catch (error) {
                console.error('Error updating feature in Supabase:', error);
                throw new Error(`Failed to update feature: ${error.message}`);
            }
        }

        // Delete a feature via Supabase API
        async function deleteFeatureFromSupabase(featureId) {
            try {
                console.log('Deleting feature from Supabase:', featureId);

                const response = await fetch(`${API_BASE_URL}/features/${featureId}`, {
                    method: 'DELETE',
                    signal: AbortSignal.timeout(15000)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || 'Failed to delete feature');
                }
            } catch (error) {
                console.error('Error deleting feature from Supabase:', error);
                throw new Error(`Failed to delete feature: ${error.message}`);
            }
        }



        // Vote on a feature via Supabase API
        async function voteOnFeature(featureId, voteType, userId) {
            try {
                validateVoteParams(featureId, voteType, userId);
                console.log('Voting on feature:', featureId, 'Vote type:', voteType, 'User:', userId);

                const response = await fetch(`${API_BASE_URL}/vote`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        featureId: featureId,
                        voteType: voteType,
                        userId: userId
                    }),
                    signal: AbortSignal.timeout(15000)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    return result;
                } else {
                    throw new Error(result.error || 'Failed to record vote');
                }
            } catch (error) {
                console.error('Error voting on feature:', error);
                throw new Error(`Failed to vote: ${error.message}`);
            }
        }

        // Test functions for debugging (call from browser console)
        window.testSupabaseAPI = async function() {
            console.log('Testing Supabase API integration...');
            console.log('Protocol:', window.location.protocol);

            const testFeature = {
                id: 'test-' + Date.now(),
                title: 'Test Feature Request',
                description: 'This is a test feature request to verify Supabase integration.',
                votes: 0,
                date: new Date().toISOString(),
                creatorId: 'test-user'
            };

            try {
                const result = await addFeatureToSupabase(testFeature);
                console.log('✅ Supabase API test successful:', result);
                return { success: true, result };
            } catch (error) {
                console.error('❌ Supabase API test failed:', error);
                return { success: false, error: error.message };
            }
        };

        window.testVoting = async function(featureId = null) {
            console.log('Testing voting system...');

            if (!featureId && features.length > 0) {
                featureId = features[0].id;
            }

            if (!featureId) {
                console.error('No feature ID provided and no features available');
                return { success: false, error: 'No feature available to test voting' };
            }

            const userId = getUserId();
            console.log('Testing vote on feature:', featureId, 'by user:', userId);

            try {
                const result = await voteOnFeature(featureId, 'up', userId);
                console.log('✅ Voting test successful:', result);
                return { success: true, result };
            } catch (error) {
                console.error('❌ Voting test failed:', error);
                return { success: false, error: error.message };
            }
        };
        // Handle form submission - now uses Supabase backend
        featureForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();

            if (!title || !description) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            // Show loading state
            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Uploading...';
            submitButton.disabled = true;

            try {
                // Get or create user ID
                let userId = localStorage.getItem('tripwisego-user-id');
                if (!userId) {
                    userId = Date.now().toString();
                    localStorage.setItem('tripwisego-user-id', userId);
                }

                const newFeature = {
                    id: Date.now().toString(),
                    title: title,
                    description: description,
                    votes: 0,
                    date: new Date().toISOString(),
                    creatorId: userId
                };

                // Save to Supabase
                const savedFeature = await addFeatureToSupabase(newFeature);

                // Update local display cache
                features.unshift(savedFeature);
                localStorage.setItem('tripwisego-features-cache', JSON.stringify(features));
                displayFeatures();

                // Reset form and close modal
                featureForm.reset();
                featureModal.classList.add('hidden');

                // Show success message
                showNotification('Feature request uploaded successfully!', 'success');

                // Scroll to the new feature
                setTimeout(() => {
                    const firstCard = document.querySelector('.post-card');
                    if (firstCard) {
                        firstCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                }, 100);

            } catch (error) {
                console.error('Error submitting feature:', error);

                let errorMessage = 'Failed to upload feature request.';
                if (error.message.includes('Network error') || error.message.includes('Failed to fetch')) {
                    errorMessage = 'Network error: Unable to connect to Google Apps Script. Please check your internet connection.';
                } else if (error.message.includes('timeout')) {
                    errorMessage = 'Request timeout: Google Apps Script took too long to respond. Please try again.';
                } else {
                    errorMessage = error.message || errorMessage;
                }

                showNotification(errorMessage, 'error');

            } finally {
                // Reset button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }
        });
        // Update connection status indicator
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connection-status');
            const isFileProtocol = window.location.protocol === 'file:';

            if (isFileProtocol) {
                statusElement.innerHTML = '<i class="fas fa-file mr-1"></i> File Mode';
                statusElement.className = 'text-sm px-3 py-1 rounded-full bg-blue-100 text-blue-800';
            } else {
                statusElement.innerHTML = '<i class="fas fa-cloud mr-1"></i> Web Mode';
                statusElement.className = 'text-sm px-3 py-1 rounded-full bg-green-100 text-green-800';
            }
        }

        // Initialize the application
        async function initializeApp() {
            updateConnectionStatus();

            // Load features from Supabase
            const success = await loadFeaturesFromSupabase();

            if (!success) {
                console.log('Failed to load from Supabase, showing cached data if available');
            }
        }

        // Refresh features from Supabase
        async function refreshFeatures() {
            const originalContent = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Refreshing...';
            refreshBtn.disabled = true;

            try {
                const success = await loadFeaturesFromSupabase();
                if (success) {
                    showNotification('Features refreshed successfully!', 'success');
                } else {
                    showNotification('Failed to refresh features. Showing cached data.', 'error');
                }
            } catch (error) {
                console.error('Error refreshing features:', error);
                showNotification('Failed to refresh features: ' + error.message, 'error');
            } finally {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            }
        }

        // Load features from Supabase on page load
        initializeApp();

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transition-all duration-300 transform translate-x-full`;

            if (type === 'success') {
                notification.classList.add('bg-green-500');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500');
            } else {
                notification.classList.add('bg-blue-500');
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Animate out and remove
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        // Handle voting - now fully functional with Supabase integration
        async function handleVote(e) {
            e.preventDefault();

            const button = e.target.closest('.vote-button');
            if (!button) return;

            const featureId = button.getAttribute('data-id');
            const voteType = button.getAttribute('data-vote'); // 'up' or 'down'
            const userId = getUserId();

            console.log('Vote clicked:', { featureId, voteType, userId });

            // Check if user has already voted on this feature
            if (hasUserVoted(featureId, userId)) {
                const existingVote = getUserVote(featureId, userId);
                if (existingVote.voteType === voteType) {
                    showNotification('You have already voted on this feature!', 'error');
                    return;
                } else {
                    // User is changing their vote
                    showNotification(`Changing your vote from ${existingVote.voteType} to ${voteType}...`, 'info');
                }
            }

            // Disable the button temporarily
            button.disabled = true;
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            try {
                // Submit vote to Supabase
                const result = await voteOnFeature(featureId, voteType, userId);

                if (result.success !== false) {
                    // Record the vote locally
                    recordUserVote(featureId, userId, voteType);

                    // Update the vote count in the UI immediately
                    updateVoteCountInUI(featureId, voteType, hasUserVoted(featureId, userId));

                    // Show success message
                    showNotification('Vote recorded successfully!', 'success');

                    // Update the features array for sorting
                    const feature = features.find(f => f.id === featureId);
                    if (feature) {
                        if (voteType === 'up') {
                            feature.votes = (feature.votes || 0) + 1;
                        } else {
                            feature.votes = Math.max((feature.votes || 0) - 1, 0);
                        }

                        // Save updated features to cache
                        localStorage.setItem('tripwisego-features-cache', JSON.stringify(features));

                        // Re-display features to update sorting if needed
                        if (sortSelect.value === 'votes') {
                            displayFeatures();
                        }
                    }
                } else {
                    throw new Error(result.error || 'Failed to record vote');
                }

            } catch (error) {
                console.error('Error voting:', error);

                // Show specific error message
                let errorMessage = 'Failed to record vote.';
                if (error.message.includes('Network error')) {
                    errorMessage = 'Network error: Unable to connect for voting. Please check your internet connection.';
                } else if (error.message.includes('timeout')) {
                    errorMessage = 'Request timeout: Voting took too long. Please try again.';
                } else if (error.message.includes('duplicate')) {
                    errorMessage = 'You have already voted on this feature.';
                } else {
                    errorMessage = error.message || errorMessage;
                }

                showNotification(errorMessage, 'error');

            } finally {
                // Re-enable the button
                button.disabled = false;
                button.innerHTML = originalContent;
            }
        }

        // Update vote count in the UI immediately
        function updateVoteCountInUI(featureId, voteType, hadPreviousVote) {
            // Find the vote count element for this feature
            const voteButtons = document.querySelectorAll(`[data-id="${featureId}"]`);
            let voteCountElement = null;

            // Find the vote count span (it's between the up and down buttons)
            voteButtons.forEach(button => {
                const parent = button.parentElement;
                const voteSpan = parent.querySelector('span');
                if (voteSpan && voteSpan.textContent.match(/^\d+$/)) {
                    voteCountElement = voteSpan;
                }
            });

            if (voteCountElement) {
                let currentVotes = parseInt(voteCountElement.textContent) || 0;

                if (voteType === 'up') {
                    currentVotes += 1;
                } else if (voteType === 'down') {
                    currentVotes = Math.max(currentVotes - 1, 0);
                }

                // Update the display
                voteCountElement.textContent = currentVotes;

                // Add a brief animation to highlight the change
                voteCountElement.style.transform = 'scale(1.2)';
                voteCountElement.style.color = voteType === 'up' ? '#10b981' : '#ef4444';

                setTimeout(() => {
                    voteCountElement.style.transform = 'scale(1)';
                    voteCountElement.style.color = '';
                }, 300);

                console.log(`Updated vote count for feature ${featureId}: ${currentVotes}`);
            } else {
                console.warn(`Could not find vote count element for feature ${featureId}`);
            }
        }

        // Update vote button states to show user's previous votes
        function updateVoteButtonStates() {
            const userId = getUserId();

            document.querySelectorAll('.vote-button').forEach(button => {
                const featureId = button.getAttribute('data-id');
                const voteType = button.getAttribute('data-vote');
                const userVote = getUserVote(featureId, userId);

                if (userVote && userVote.voteType === voteType) {
                    // User has voted with this button
                    button.classList.add('voted');
                    if (voteType === 'up') {
                        button.classList.add('text-green-600');
                        button.classList.remove('text-gray-500');
                    } else {
                        button.classList.add('text-red-600');
                        button.classList.remove('text-gray-500');
                    }
                } else {
                    // User hasn't voted with this button
                    button.classList.remove('voted', 'text-green-600', 'text-red-600');
                    button.classList.add('text-gray-500');
                }
            });
        }

        // Handle deletion - now uses Google Apps Script
        async function handleDelete(e) {
            const button = e.target.closest('[data-action="delete"]');
            if (!button) return;

            const featureId = button.getAttribute('data-id');
            if (!featureId) return;

            // Confirm deletion
            if (!confirm('Are you sure you want to delete this feature request?')) {
                return;
            }

            // Show loading state
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deleting...';
            button.disabled = true;

            try {
                // Delete from Google Apps Script
                await deleteFeatureFromGoogleAppsScript(featureId);

                // Remove from local cache
                features = features.filter(f => f.id !== featureId);
                localStorage.setItem('tripwisego-features-cache', JSON.stringify(features));
                displayFeatures();

                showNotification('Feature request deleted successfully!', 'success');

            } catch (error) {
                console.error('Error deleting feature:', error);
                showNotification('Failed to delete feature request: ' + error.message, 'error');

                // Reset button state on error
                button.innerHTML = originalContent;
                button.disabled = false;
            }
        }
        // Event Listeners
        openFormBtn.addEventListener('click', () => {
            featureModal.classList.remove('hidden');
        });

        closeModalBtn.addEventListener('click', () => {
            featureModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        featureModal.addEventListener('click', (e) => {
            if (e.target === featureModal) {
                featureModal.classList.add('hidden');
            }
        });

        // Sort change handler
        sortSelect.addEventListener('change', displayFeatures);

        // Refresh button handler
        refreshBtn.addEventListener('click', refreshFeatures);

        // Initial display
        displayFeatures();
    </script>

</body>

</html>